service: ratemymouth
image: teyermann/ratemymouth

servers:
  web:
    - ratemymouth.dotsncircles.com
  worker:
    hosts:
      - ratemymouth.dotsncircles.com
    cmd: bin/rails solid_queue:start

proxy:
  ssl: true
  host: ratemymouth.dotsncircles.com
  app_port: 3000

registry:
  username: teyermann
  password:
    - KAM<PERSON>_REGISTRY_PASSWORD

builder:
  arch: arm64

env:
  secret:
    - RAILS_MASTER_KEY

ssh:
  user: admin
  proxy: <EMAIL>

volumes:
  - "/home/<USER>/docker_storage/ratemymouth_db/:/rails/storage"

deploy_timeout: 90
drain_timeout: 90
