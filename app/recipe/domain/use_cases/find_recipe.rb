module Recipe
  module Domain
    module UseCases
      class FindRecipe
        def initialize(repository: ::Recipe)
          @repository = repository
        end

        def call(id:, includes: [])
          recipe = if includes.any?
            @repository.includes(*includes).find(id)
          else
            @repository.find(id)
          end

          recipe
        end

        private

        attr_reader :repository
      end
    end
  end
end
