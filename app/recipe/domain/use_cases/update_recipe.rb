module Recipe
  module Domain
    module UseCases
      class UpdateRecipe
        def initialize(repository: ::Recipe)
          @repository = repository
        end

        def call(id:, attributes:)
          recipe = @repository.find(id)

          if recipe.update(attributes)
            { success: true, recipe: recipe }
          else
            { success: false, errors: recipe.errors }
          end
        end

        private

        attr_reader :repository
      end
    end
  end
end
