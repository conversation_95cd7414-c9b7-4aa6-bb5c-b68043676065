name: CI

on:
  pull_request:
  push:
    branches: [main]

jobs:
  scan_ruby:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Ruby
        uses: ruby/setup-ruby@v1
        with:
          ruby-version: .ruby-version
          bundler-cache: true

      - name: Scan for common Rails security vulnerabilities using static analysis
        run: bin/brakeman --no-pager

  scan_js:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Ruby
        uses: ruby/setup-ruby@v1
        with:
          ruby-version: .ruby-version
          bundler-cache: true

      - name: Scan for security vulnerabilities in JavaScript dependencies
        run: bin/importmap audit

  lint:
    runs-on: ubuntu-latest
    permissions:
      checks: write
      contents: write
    steps:
      - name: Standard Ruby linting
        uses: standardrb/standard-ruby-action@v1

  test:
    runs-on: ubuntu-latest
    steps:
      - name: Install packages
        run: sudo apt-get update && sudo apt-get install --no-install-recommends -y build-essential git curl libjemalloc2 libvips sqlite3 npm libyaml-dev pkg-config libglib2.0-dev libexpat1-dev libvips42

      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: "23"
          cache: "npm"

      - name: Install JavaScript dependencies
        run: npm install

      - name: Set up Ruby
        uses: ruby/setup-ruby@v1
        with:
          ruby-version: .ruby-version
          bundler-cache: true

      - name: Build Vite assets
        run: bin/vite build --clear --mode=test

      - name: Precompile assets
        env:
          RAILS_ENV: test
        run: bin/rails assets:precompile

      - name: Run tests
        env:
          RAILS_ENV: test
          # REDIS_URL: redis://localhost:6379/0
        run: bin/rails db:test:prepare test test:system

      - name: Keep screenshots from failed system tests
        uses: actions/upload-artifact@v4
        if: failure()
        with:
          name: screenshots
          path: ${{ github.workspace }}/tmp/screenshots
          if-no-files-found: ignore
